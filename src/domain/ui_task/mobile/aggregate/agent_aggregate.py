#!/usr/bin/env python3
"""
Agent聚合器

统一管理和对外暴露DecisionAgent、ExecutionAgent和SupervisorAgent
不直接暴露单个agent，而是通过聚合器提供统一接口
"""

from typing import Dict, Any, Tuple

from .agent.decision_agent import DecisionAgent
from .agent.execution_agent import ExecutionAgent
from .agent.supervisor_agent import SupervisorAgent
from ..repo.do.State import DeploymentState


class AgentAggregate:
    """Agent聚合器 - 统一管理三个agent的对外接口"""

    def __init__(self):
        """初始化三个agent实例"""
        self._decision_agent = DecisionAgent()
        self._execution_agent = ExecutionAgent()
        self._supervisor_agent = SupervisorAgent()

    def analyze_and_decide(self, state: DeploymentState, before_screenshot_path: str) -> tuple:
        """
        分析截图和测试用例上下文，生成思考过程和决定下一步动作
        
        Args:
            state: 当前状态
            before_screenshot_path: 截图的base64数据
            
        Returns:
            Tuple[parsed_fields, action_line]: 解析的JSON字段、动作命令
        """
        return self._decision_agent.analyze_and_decide(state, before_screenshot_path)

    def execute_action(self, action_command: str, decision_fields: Dict[str, Any], state: DeploymentState,
                       before_screenshot_path: str, current_action=None) -> Dict[str, Any]:
        """
        执行动作，包括坐标补充和具体执行

        Args:
            action_command: 动作命令
            decision_fields: 决策agent的结构化字段
            state: 当前状态
            before_screenshot_path: 当前截图的路径（保持兼容性，但会重新截图）
            current_action: 当前动作记录对象（用于更新数据库）

        Returns:
            执行结果
        """
        return self._execution_agent.execute_action(action_command, decision_fields, state, before_screenshot_path, current_action)

    def supervise_execution(self, state: DeploymentState, app_package: str) -> DeploymentState:
        """
        监督执行过程，检查应用状态和执行限制
        
        Args:
            state: 当前状态
            app_package: 应用包名
            
        Returns:
            更新后的状态
        """
        return self._supervisor_agent.supervise_execution(state, app_package)

    def check_device_connection(self, device_id: str, task_id: str) -> bool:
        """
        检查设备连接状态

        Args:
            device_id: 设备ID
            task_id: 任务ID

        Returns:
            设备是否连接
        """
        return self._supervisor_agent.check_device_connection(device_id, task_id)

    @staticmethod
    def requires_coordinates(action_str: str) -> bool:
        """
        判断动作是否需要坐标

        Args:
            action_str: 动作字符串

        Returns:
            是否需要坐标
        """
        return ExecutionAgent.requires_coordinates(action_str)


agent_aggregate = AgentAggregate()
