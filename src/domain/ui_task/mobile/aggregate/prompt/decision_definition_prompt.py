from typing import Dict, List

from src.domain.ui_task.mobile.repo.do.State import DeploymentState




def get_decision_definition_prompt(state: DeploymentState, execution_records: List[Dict],
                                   has_execution_history: bool) -> str:
    current_execution_count = state.get("execution_count", 0)

    system_instruction = f'''
########## 角色定位 ##########
{get_role_definition(state)}

########## 测试用例信息 ##########
{get_test_case_description(state)}

########## 动作列表 ##########
{get_action_list()}

########## UI组件操作说明 ##########
{get_ui_component_instructions(state)}

########## 特殊场景 ##########
{get_special_scenarios(state)}

########## 执行记忆 ##########
{get_execute_history(execution_records)}

########## 界面截图说明 ##########
{get_screenshot_description(current_execution_count, has_execution_history)}

########## 准星标记 ##########
{get_red_mark_prompt()}

########## 图片元素提取器 ##########
{get_image_element_extractor_prompt()}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 步骤预期结果验证 ##########
{get_step_verification_failure_context(state)}

########### 回滚逻辑 ##########
{get_rollback_guidance()}

########## 自检流程 ##########
{get_self_check_prompt()}

########## 执行步骤流程 ##########
{get_execution_step_rule_prompt()}

########## 动作决策 ##########
{get_action_decision_prompt()}

########## 输出格式 ##########
{get_output_example()}

########## 输出要求 ##########
{get_output_requirement()}'''
    # print(system_instruction)
    return system_instruction


# ########### 输出字段说明 ##########
# {get_keyword_decision()}

def get_role_definition(state: DeploymentState) -> str:
    """获取角色定义"""
    return f"""你是一个专业的测试用例执行决策Agent，专门负责安卓软件的UI自动化测试决策。
**核心职责**：
1. **分析测试场景**：基于截图和测试用例要求，分析当前界面状态和执行进度
2. **制定执行决策**：为当前步骤制定精确的执行策略和具体动作指令


**合作伙伴**：
1. **执行Agent**：它会根据你输出的决策和动作指令执行具体的UI操作，过程中可能会产生操作错误，你要考虑出错情况，及时纠正
2. **监督Agent**：它会监督你的决策和执行结果，如果步骤执行失败，它会在长期存在<执行记忆> 和 <步骤预期结果验证>，重新决策时你需要考虑最近的失败原因
"""


def get_image_element_extractor_prompt() -> str:
    return """
1.仔细阅读分析图片，拆解图片中元素，定位与当前'用例步骤'描述相关的元素
  - 记录所有被定位的元素特征(文字、形状等)、元素绝对位置、相对于相邻元素的相对位置，保存到你的记忆中
"""


def get_test_case_description(state: DeploymentState) -> str:
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description")
    expected_result = state.get("expected_result", "")
    content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{test_case_description}
- **期望结果**: {expected_result}"""
    print(content)
    return content


def get_action_list() -> str:
    """获取动作列表"""
    return """1.click
  作用：模拟用户点击界面元素
  参数：无
2.long_press
  作用：模拟用户长按行为
  参数：无
3.drag
  作用：模拟抓取拖拽操作，将元素从a点拖拽到b点
  参数：无
4.scroll(direction='up/down/left/right')
  作用：滑动手机屏幕
  参数：direction='up/down/left/right'
  操作说明：
    - 向下滑动：direction='down'
    - 向上滑动：direction='up'
    - 向左滑动：direction='left'
    - 向右滑动：direction='right'
  滑动原则：
  *如'用例步骤'给出滑动方向，务必先遵循'用例步骤'给出的方向*
  *根据界面分析，判断是否到了同向尽头(列表元素无明显变化)，如果是，则应该反方向滑动持续到另一端的同向尽头*
  1. 向下滑动，为了露出纵向列表上方元素，如果纵向列表上方元素无明显变化则说明到了同向尽头
  2. 向上滑动，为了露出纵向列表下方元素，如果纵向列表下方元素无明显变化则说明到了同向尽头
  3. 向左滑动，为了露出横向列表右方元素，如果横向列表右方元素无明显变化则说明到了同向尽头
  4. 向右滑动，为了露出横向列表左方元素，如果横向列表左方元素无明显变化则说明到了同向尽头
 

5.type(content='输入的内容')
  作用：模拟输入内容
  参数：content='输入的内容'
  操作说明：
    - 输入内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面底部出现输入法，才能使用type输入内容
    - 需要删除原有内容，请先调用delete
6.delete(content=删除文字数量)
  作用：模拟删除输入框内容，content参数指定删除的字符个数
  参数：content=删除文字数量
  操作说明：
    - 删除内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面底部出现输入法，才能使用delete删除内容
7.back
  作用：模拟 Android 返回键
  参数：无
  操作说明：
    - 用于返回上一级页面，或关闭弹窗
8.wait(seconds=等待秒数)
  作用：等待一段时间
  参数：seconds=等待秒数，单位秒，默认值为5秒
  操作说明：
    - 用于页面加载、动画、广告、弹窗自动消失。
9.enter
  作用：模拟回车键
  参数：无
  操作说明：
    - 优先使用搜索按钮进行搜索；
    - 当找不到搜索按钮时使用，用于搜索框输入内容后直接搜索；
10.finished(content='执行结果')
  描述：测试用例执行完成
  参数：content,测试用例执行结果
  操作说明：
    - 整个测试用例步骤执行完毕，达成<测试用例信息>期望结果，调用finished，并填写content说明结果
11.failed(content='失败原因 ')
  作用：测试用例步骤执行失败
  参数：content失败原因  
  操作说明：
    - 整个测试用例步骤执行完毕，未达成<测试用例信息>期望结果，调用failed，并填写content说明结果
    - 无法推进测试用例执行，主动退出测试用例执行
    """


# 1.**日期选择器**
#  - **组件结构：**
#   1.日期选择器分为三行，中间行为当前日期，通过点击选中中间行年月日来修改日期，禁止使用scroll滑动修改日期
#   2.只允许点击中间一行（年/月/日），禁止点击上下行日期。
#     - 正确示例：要修改'1995'，则只能点击'1995'，让'1995'处于选中状态，被蓝色背景覆盖
#     - 错误示例：要修改'1995'，点击上下行的'1994'或'1996'，'1995'未被选中
#  - **操作方法：**
#   1.使用click点击中间一行要修改的日期，选中日期，日期数字被选中，必须呼出输入法
#   2.调用delete删除被选中日期，必须必须呼出输入法才能删除
#   3.调用type动作输入新日期数字，界面底部必须呼出输入法，并且已经删除旧日期

def get_ui_component_instructions(state: DeploymentState) -> str:
    """获取UI组件操作说明"""
    custom_instructions = state.get("ui_component_instructions")
    if custom_instructions:
        return custom_instructions
    return """
1.**日期选择器**
 - **组件结构：**
  1.日期选择器有三列，分别为年、月、日
 - **操作方法：**
  1.使用click点击中间一行要修改的日期，选中日期，日期数字被选中，必须呼出输入法
  2.调用delete删除被选中日期，必须必须呼出输入法才能删除
  3.调用type动作输入新日期数字，界面底部必须呼出输入法，并且已经删除旧日期

2.**输入框** 
 - **操作方法：**
  1.输入框输入内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面底部必须看到输入法
  2.如果输入框很大很宽，必须点击输入框第一行位置呼出输入法
  3.如果需要修改内容，需要调用delete删除原有内容

3.**按钮操作规范**
 - **操作方法：**
  1.点击前要区分是否可被点击，灰色按钮一般为不可点击，蓝色或其他颜色一般为可点击
  2.多次点击按钮没反应则说明按钮当前不可点击，重新思考，需要先满足点击条件

4.**导航栏/标签栏操作规范**
 - **组件结构：** 
  1.导航栏/标签栏包含多个标签
 - **操作方法：** 
  1.选择导航栏/标签栏，每次必须选择滑动方向的第二个“元素”作为滑动起点，不要以贴近边缘的标签作为滑动起点，向左或向右滑动
  2.必须在输出指令字段描述作为滑动起点标签的'内容'
"""
#   1.每次都以第二或第三个元素为滑动起点，向左或向右滑动


def get_special_scenarios(state: DeploymentState) -> str:
    """获取特殊场景处理说明"""
    custom_scenarios = state.get("special_scenarios")
    return f"""
* 如界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失；
* 如界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗；
* 如界面边缘存在悬浮气泡遮挡了目标元素位置，可以先通过拖动(drag)上/下移动悬浮气泡，露出目标元素；
* 如界面出现未加载完成、空白页面、页面切换中、异常页面，则调用wait(seconds=3)等待加载完成，其中若'空白页'和'异常页'出现连续多次(>2次)等待则考虑系统问题；
{custom_scenarios}
* 如以上条件都不满足，则调用back返回上一级；"""


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
    #  return """1. 结合 <执行记忆界面截图> 、<当前轮界面截图> 和 <测试用例信息> 的'用例执行步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
    # - 在界面中定位与'用例步骤'的元素，必须遵循'用例步骤'描述的**元素特征**和**位置信息**
    # - 禁止伪造、猜测不存在的元素和内容。"""
    return """1. 结合 <当前轮界面截图> 和 <测试用例信息> 的'用例执行步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
   - 在界面中定位与<测试用例信息>的'用例步骤'的相关元素，必须严格按照'用例步骤'描述的**元素特征**和**位置信息**
   - 优先根据 <测试用例信息>的'用例步骤'中描写的方位去聚焦并定位元素，要确定元素特征和位置的唯一性
   - 禁止伪造、猜测不存在的元素和内容。"""


def get_red_mark_prompt() -> str:
    return """1. 准星标记，用于定位已经被点击的元素
2. 准星标记外层一个红圈，中心有一个红色实心圆点"""


def get_rollback_guidance() -> str:
    return """1.查看<执行记忆>中上一轮执行动作，按照以下方式进行回滚；
  - 当上一轮动作是click、long_press：
    - 考虑根据<当前轮界面截图>实际情况进行返回(back)或点击返回/关闭按钮，或根据'截图'、<测试用例信息>和<执行记忆>重新决策
  - 当上一轮动作是scroll：
    - *若经历2轮同向滑动后，结合<执行记忆>，<当前轮界面截图>和<执行记忆界面截图>，判断是否到了同向尽头(列表元素无明显变化)，应该方向滑动持续到另一端尽头*
  - 如果最近一个步骤多次经历上述场景确实没找到目标元素,可根据<测试用例>的'用例步骤'、<执行记忆> 和<当前轮界面截图> 自行决策动作；
"""
# 3. 根据的最近5轮的<执行记忆>，如果同向连续滑动 >=4次都无法找到目标元素，则需要反方向滑动以继续搜索目标；如都找不到可能进入了错误页面进行返回到上一步或调用 Failed 动作结束流程

def get_self_check_prompt() -> str:
    """获取自检流程提示"""
    return """请严格按照以下规则进行自检判断，并返回明确结论。每一项检测必须完成，不允许省略或模糊判断。
1. **上一轮操作检测**
    - 回顾 <执行记忆> 上一步的目标元素（特征与位置信息）与<测试用例信息>的'用例步骤'描述的目标元素进行比对是否一致 ，再与 <执行记忆界面截图> 上一轮界面中被 <准星标记> 的元素进行比对；如不一致，则判操作有误，需重新决策(在当前页面继续操作或返回上一页面)。

2. **用例路径偏离检测**
    - 结合<执行记忆>的操作和<测试用例信息>的'用例步骤'分析，发现最近3轮脱离了'用例步骤'(与测试用例路径不符)，导致无法按原定<测试用例信息>中的用例步骤提示的动作继续推进，则调用 failed 动作结束流程
    - 如存在‘空白页’、‘错误页’、‘异常页’等非正常页面，可根据<特殊场景>进行处理
    
3. **无法寻找目标元素检测**
    - 根据<执行记忆>
        - 如果是在同一界面的纵或横向列表寻找，需确认已滑动至两端同向尽头(列表元素无明显变化)都没有找到目标元素，则判定为寻找目标元素失败，立即调用failed 动作结束流程

4. **用例无法推进检测**
    - 根据<执行记忆>
        - 最近5轮，在相同步骤通过非滑动方式寻找目标元素时, 仍无法找到目标元素推进用例步骤，立即调用failed 动作结束流程
        - 最近4轮，在相同步骤通过非滑动方式寻找目标元素时, 找到目标元素进行操作，但无法推进用例步骤，考虑重新理解'用例步骤'的含义，进行重新决策或直接调用 failed 动作结束流程
  
5. **步骤完成检测**
    * 检测目的
     - 为了检测上一步骤是否已经完成，可以切换到下一个步骤
    * 判定规则:
       0.根据<执行记忆>、<当前轮界面截图>和<测试用例信息>的步骤判断上一步骤是否已经完成(如: 没有找到目标元素)，上一步骤未完成则需要重新决策
       1.如果<步骤预期结果验证>存在'验证失败原因'，则上一步骤执行未完成或失败
         - 根据'验证失败原因'结合 <执行记忆> 和 <测试用例信息>的'用例步骤'，使用<图片元素提取器>分析<当前轮界面截图> 重新描述需要操作元素即可；
       2.在<当前轮界面截图>无法找到<测试用例信息>中的下一'用例步骤'需要操作的元素，则上一步骤可能执行未完成或操作错误，调用<回滚逻辑> 进行重新决策；    

*'重新决策'时，重新回顾整个<测试用例信息>的'用例步骤'和<执行记忆>曾操作的步骤，从头开始回顾(详细分析'用例步骤'的描述元素特征，是否理解错了)，然后重新选择在哪一步骤开始<动作决策>*     
"""



def get_action_decision_prompt() -> str:
    # 2. 获取<执行记忆>最近3轮执行决策、执行动作和执行步骤，并结合'记忆中的相关元素'，确定下一个执行动作和目标元素；
    return f"""1. 通过<图片元素提取器>分析<当前轮界面截图>，仅以本轮截图中“可见且可操作”的元素为依据，提取“当前用例步骤”需操作的目标相关元素，并保存到你的记忆中。
2. 结合<执行记忆>，在确保动作落地基于“本轮可见元素”的前提下，结合“记忆中的相关元素”和<测试用例信息>的“用例步骤”描述，确定下一个操作动作和目标元素：
   - 首先严格依据“用例步骤”中的页面方位/容器/序号进行定位，在该方位内确保目标唯一
   - 一定要清晰识别'用例步骤'描述的**元素特征(文字内容、形状)**和**位置信息**
   - 当<当前轮界面截图>出现同名或相似元素时，结合<执行记忆>和“用例步骤”的元素特征与相对位置信息进行消歧；
   - 保持与既往决策的连贯性：对齐已知页面/模式/列表索引与已操作记录，避免重复或自相矛盾；若记忆与现屏冲突，以截图为准并在行动前修正；
3. 优先考虑 <特殊场景>，按照场景说明进行操作；
4. 描述操作目标元素的时候，忽略‘公告横幅’的遮挡，可以直接描述被公告横幅遮挡的目标元素；
5. 必须优先参考<测试用例信息>的'用例步骤'描述的动作和元素特征，每一个步骤中可能存在多个执行动作；
6. 结合 <动作列表> 根据 <UI组件操作说明> 选择指定动作，如果组件不存在 <UI组件操作说明> 则使用先验知识选择指定动作；
7. 整个测试用例步骤执行完毕，达成<测试用例信息>期望结果，调用finished，如果与期望结果不一致，则调用failed；

"""


def get_execution_step_rule_prompt() -> str:
    return """1. 结合<执行记忆>严格按<测试用例信息> 的'用例步骤'顺序执行，不允许跳过用例步骤，每个用例步骤可能存在多个执行动作；"""


# - click：必须描述被点击的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
# - long_press：必须描述被长按的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
# - drag：必须描述被拖拽的具体元素特征(文字、形状等)；描述目标位置元素特征(文字、形状等)
# - scroll：必须描述滑动起点的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
def get_output_example() -> str:
    """获取输出格式要求"""
    # "interface_analysis": "当前界面(继续补充内容,保持语言简洁)",
    return """{{
"self_check": "输出<自检流程>的自检结果(保持语言简洁)",
"interface_analysis": "当前界面(继续补充内容,保持语言简洁)",
"current_step_name": "当前正在执行的步骤名称，从<测试用例信息>获取，按照步骤顺序一个个执行",
"current_step_index": "根据current_step_name值，从<测试用例信息>中获取步骤索引",
"action_decision": "输出 <动作决策> 的过程，每轮只能执行一个动作，如果一个步骤有多个动作，则需要等待下轮规划(保持语言简洁)",
"instruction": "根据'动作决策'的结果仅复述需要操作的动作及UI元素，禁止描述任何'用例步骤'目的、期望结果或判断条件(保持语言简洁)；
- 按照以下规范进行(保持语言简洁)：
    1. 仅可以描述一个动作，不能存在多个动作的描述，该动作和 action 中给出的动作是一致的，如：‘点击’
    2. 必须描述被操作的具体元素特征(文字、图形等);
    3. 描述元素绝对位置, 如：在页面的哪个区域；
    4. 描述元素相对位置, 如：在哪个元素的哪个位置 (可选项)"
"action": "动作+参数，必须参照<动作列表>"
}}"""


# 请仅输出 UI 操作动作本身（动作 + 起始元素 + 参数），禁止描述任何目的、意图、期望结果或判断条件

def get_output_requirement() -> str:
    return """
1.使用JSON格式输出内容，严格遵循<输出格式>，保证输出内容与格式完全一致
"""


def get_screenshot_description(current_execution_count, has_execution_history):
    if has_execution_history:
        screenshot_context = f"""当前是第{current_execution_count}轮执行，你能看到最近2轮的<执行记忆界面截图>和<当前轮界面截图>
 1. 执行记忆界面截图：显示最近2轮执行的界面截图，被点击的位置会有一个<准星标记>
 2. 当前轮界面截图：手机界面最新截图

**重要提示**：
 - <执行记忆界面截图>中如果存在<准星标记>，说明是该轮动作操作的具体位置
 - <准星标记>帮助你定位历史操作元素"""
    else:
        screenshot_context = f"""当前是第{current_execution_count}轮执行，你只能看到<当前轮界面截图>"""
    return screenshot_context


def get_execute_history(execution_records) -> str:
    """获取执行历史"""

    if not execution_records:
        return ""

    history_content = ""

    for i, record in enumerate(execution_records):
        decision_content = record.get("decision_content", "")
        record_execution_count = record.get("execution_count", "")
        step_verification_failure = record.get("step_verification_failure")
        record_status = record.get("status", "")

        # 显示执行记录（包括被阻塞的记录，但不显示决策内容）
        if decision_content and record_status != "blocked":
            history_content += f"""
**第{record_execution_count}轮执行**
{_escape_template_variables(decision_content)}"""

        # 显示每一轮的验证失败信息，确保所有失败信息都被保留
        if step_verification_failure:
            failure_info = f"""
- 步骤失败：{step_verification_failure['failure_reason']}"""
            history_content += failure_info
    print(history_content)
    return history_content


def get_step_verification_failure_context(state: DeploymentState) -> str:
    verification_failure_reason = state.get("verification_failure_reason")
    if verification_failure_reason:
        # 从历史记录中获取验证失败的步骤信息
        history = state.get("history", [])

        # 查找最近的验证失败记录
        verification_failure_info = None
        for record in reversed(history):
            if record.get("step_verification_failure"):
                verification_failure_info = record["step_verification_failure"]
                break

        if verification_failure_info:
            step_index = verification_failure_info.get("step_index", 0)
            step_name = verification_failure_info.get("step_name", "")
            context = f"""- 验证失败步骤：第{step_index + 1}步 - "{step_name}"
- 验证失败原因：{verification_failure_reason}"""
            state["verification_failure_reason"] = ""
            return context

    return ""


def _escape_template_variables(text: str) -> str:
    """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")
